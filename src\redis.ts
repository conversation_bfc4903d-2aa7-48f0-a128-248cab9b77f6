import { createClient, type RedisClientType } from 'redis';

class RedisManager {
  private static instance: RedisManager;
  private client: RedisClientType | null = null;
  private isConnecting = false;
  private isConnected = false;

  private constructor() {}

  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager();
    }
    return RedisManager.instance;
  }

  public async getClient(): Promise<RedisClientType> {
    if (this.client && this.isConnected) {
      return this.client;
    }

    if (this.isConnecting) {
      // Aguardar a conexão em andamento
      while (this.isConnecting) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      if (this.client && this.isConnected) {
        return this.client;
      }
    }

    return this.connect();
  }

  private async connect(): Promise<RedisClientType> {
    if (this.isConnecting) {
      throw new Error('Conexão já em andamento');
    }

    this.isConnecting = true;
    
    try {
      console.log('Iniciando conexão com Redis...');
      
      this.client = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        socket: {
          connectTimeout: 30000, // 30 segundos
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              console.log('Muitas tentativas de reconexão Redis, parando...');
              return false;
            }
            const delay = Math.min(retries * 1000, 10000); // Max 10 segundos
            console.log(`Redis reconectando em ${delay}ms (tentativa ${retries})`);
            return delay;
          }
        }
      });

      // Event listeners
      this.client.on('error', (err) => {
        console.error('Erro Redis:', err.message);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('Redis conectado');
      });

      this.client.on('ready', () => {
        console.log('Redis pronto para uso');
        this.isConnected = true;
      });

      this.client.on('reconnecting', () => {
        console.log('Redis reconectando...');
        this.isConnected = false;
      });

      this.client.on('end', () => {
        console.log('Conexão Redis encerrada');
        this.isConnected = false;
      });

      // Conectar
      await this.client.connect();
      
      // Testar conexão
      await this.client.ping();
      console.log('Conexão Redis estabelecida com sucesso!');
      
      this.isConnected = true;
      return this.client;

    } catch (error) {
      console.error('Falha ao conectar ao Redis:', error);
      this.isConnected = false;
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  public async disconnect(): Promise<void> {
    if (this.client) {
      try {
        await this.client.quit();
        console.log('Redis desconectado graciosamente');
      } catch (error) {
        console.error('Erro ao desconectar Redis:', error);
      } finally {
        this.client = null;
        this.isConnected = false;
        this.isConnecting = false;
      }
    }
  }

  public isReady(): boolean {
    return this.isConnected && this.client !== null;
  }

  // Métodos de conveniência para operações comuns
  public async safeOperation<T>(operation: (client: RedisClientType) => Promise<T>): Promise<T | null> {
    try {
      const client = await this.getClient();
      return await operation(client);
    } catch (error) {
      console.error('Erro na operação Redis:', error);
      return null;
    }
  }
}

// Exportar instância singleton
export const redisManager = RedisManager.getInstance();

// Função de conveniência para obter o cliente
export async function getRedisClient(): Promise<RedisClientType> {
  return redisManager.getClient();
}

// Função para operações seguras
export async function safeRedisOperation<T>(
  operation: (client: RedisClientType) => Promise<T>
): Promise<T | null> {
  return redisManager.safeOperation(operation);
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Recebido SIGINT, desconectando Redis...');
  await redisManager.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Recebido SIGTERM, desconectando Redis...');
  await redisManager.disconnect();
  process.exit(0);
});
