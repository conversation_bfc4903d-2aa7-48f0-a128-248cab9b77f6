version: '3.8'

services:
  # Aplicação principal
  wplace-proxy:
    build: .
    container_name: wplace-proxy
    ports:
      - "3000:3000"
    environment:
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      - REDIS_URL=redis://redis:6379
      - NODE_ENV=${NODE_ENV:-development}
    volumes:
      # Persistir artworks e tiles
      - ./artworks:/usr/src/app/artworks
      - ./tiles:/usr/src/app/tiles
      # Para desenvolvimento, montar o código fonte
      - ./src:/usr/src/app/src:ro
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - wplace-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/stats"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis para armazenamento de dados
  redis:
    image: redis:7-alpine
    container_name: wplace-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - wplace-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Nginx como reverse proxy (opcional, para produção)
  nginx:
    image: nginx:alpine
    container_name: wplace-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - wplace-proxy
    restart: unless-stopped
    networks:
      - wplace-network
    profiles:
      - production

  # Redis Commander para administração do Redis (desenvolvimento)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: wplace-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - wplace-network
    profiles:
      - development

volumes:
  redis_data:
    driver: local

networks:
  wplace-network:
    driver: bridge